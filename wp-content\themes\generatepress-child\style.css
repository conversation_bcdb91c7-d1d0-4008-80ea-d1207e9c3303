/*
Theme Name: GeneratePress Child - Petting Zoo Directory
Description: Child theme of GeneratePress for the Petting Zoo Directory website
Author: Custom Development
Template: generatepress
Version: 2.0.0
Text Domain: generatepress-child
*/

/* Import parent theme styles */
@import url("../generatepress/style.css");

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800&family=Inter:wght@400;500;600&display=swap');

/* ========================================
   DESIGN SYSTEM - NATURAL & TRUSTWORTHY
   ======================================== */

/* Color Palette - Natural, Earthy, Family-Friendly */
:root {
    --forest-green: #2F6130;
    --forest-green-light: #4a7c4d;
    --forest-green-dark: #1e3f20;
    --warm-brown: #8B5E3C;
    --warm-brown-light: #a67350;
    --soft-beige: #FDF7F0;
    --soft-beige-dark: #f5ede0;
    --sky-blue: #7DC8F7;
    --sky-blue-light: #9dd4f9;
    --sky-blue-dark: #5bb8f4;
    --dark-charcoal: #333333;
    --medium-gray: #666666;
    --light-gray: #f8f9fa;
    --white: #ffffff;
    --success-green: #28a745;
    --warning-yellow: #ffc107;
    --danger-red: #dc3545;
}

/* Typography - Friendly & Accessible */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 20px;
    line-height: 1.6;
    color: var(--dark-charcoal);
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: var(--dark-charcoal);
}

h1 { font-size: 2.5rem; font-weight: 800; }
h2 { font-size: 2rem; font-weight: 700; }
h3 { font-size: 1.5rem; font-weight: 600; }
h4 { font-size: 1.25rem; font-weight: 600; }

/* Enhanced Hero Section - Full Width with Background Image */
.hero-section {
    position: relative;
    width: 100%;
    height: 80vh;
    min-height: 600px;
    max-height: 800px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}

/* Ensure full width for hero section in GeneratePress */
.full-width-content .hero-section {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
}

/* Override GeneratePress container constraints for hero */
.hero-section .container {
    max-width: none;
    width: 100%;
}

/* Ensure GeneratePress doesn't interfere with full width */
.full-width-content {
    width: 100% !important;
    max-width: none !important;
}

.full-width-content .site-main {
    width: 100% !important;
    max-width: none !important;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
}

/* Fallback background if image doesn't load */
.hero-section {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--warm-brown) 100%);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(47, 97, 48, 0.8) 0%, rgba(30, 63, 32, 0.9) 100%);
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
    color: var(--white);
    text-align: center;
    width: 100%;
    padding: 0 20px;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.08)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.06)"/></svg>');
    pointer-events: none;
}

.hero-section h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    font-weight: 800;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.hero-section p {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.95;
    position: relative;
    z-index: 2;
}

/* Enhanced Zoo Finder Tool - Dad-Friendly UX */
.zoo-finder {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    margin-top: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    z-index: 2;
    border: 3px solid var(--soft-beige);
}

.zoo-finder h3 {
    color: var(--forest-green);
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    font-weight: 700;
    text-align: center;
}

.zoo-finder select,
.zoo-finder input {
    width: 100%;
    padding: 16px 20px;
    margin-bottom: 1.5rem;
    border: 2px solid var(--soft-beige-dark);
    border-radius: 10px;
    font-size: 1.1rem;
    font-family: 'Inter', sans-serif;
    background: var(--soft-beige);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.zoo-finder select:focus,
.zoo-finder input:focus {
    outline: none;
    border-color: var(--sky-blue);
    box-shadow: 0 0 0 3px rgba(125, 200, 247, 0.2);
    background: var(--white);
}

.zoo-finder button {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-light) 100%);
    color: var(--white);
    padding: 16px 30px;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
    min-height: 48px; /* Mobile-friendly tap target */
    font-family: 'Nunito', sans-serif;
}

.zoo-finder button:hover {
    background: linear-gradient(135deg, var(--forest-green-light) 0%, var(--forest-green) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.3);
}

.zoo-finder .btn-secondary {
    background: linear-gradient(135deg, var(--warm-brown) 0%, var(--warm-brown-light) 100%);
    margin-bottom: 0;
}

.zoo-finder .btn-secondary:hover {
    background: linear-gradient(135deg, var(--warm-brown-light) 0%, var(--warm-brown) 100%);
    box-shadow: 0 4px 15px rgba(139, 94, 60, 0.3);
}

/* ========================================
   ENHANCED GRID LAYOUTS - MOBILE FIRST
   ======================================== */

.zoo-grid,
.city-grid,
.animal-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

/* Enhanced Zoo Card Design - Trust & Visual Hierarchy */
.zoo-card {
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid var(--soft-beige);
    position: relative;
}

.zoo-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(47, 97, 48, 0.15);
    border-color: var(--forest-green);
}

.zoo-card img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.zoo-card:hover img {
    transform: scale(1.05);
}

.zoo-card-content {
    padding: 2rem;
    position: relative;
}

.zoo-card h3 {
    margin-bottom: 0.75rem;
    color: var(--forest-green);
    font-size: 1.3rem;
    font-weight: 700;
}

.zoo-card .location {
    color: var(--medium-gray);
    margin-bottom: 1.25rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.zoo-card .location::before {
    content: "📍";
    font-size: 1.1rem;
}

.zoo-card .features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.zoo-card .feature-tag {
    background: linear-gradient(135deg, var(--soft-beige) 0%, var(--soft-beige-dark) 100%);
    color: var(--forest-green);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    border: 1px solid var(--forest-green);
    transition: all 0.3s ease;
}

.zoo-card .feature-tag:hover {
    background: var(--forest-green);
    color: var(--white);
    transform: translateY(-2px);
}

/* ========================================
   ENHANCED SECTION STYLES - VISUAL HIERARCHY
   ======================================== */

.section {
    padding: 5rem 0;
    position: relative;
}

.section-title {
    text-align: center;
    font-size: 2.75rem;
    margin-bottom: 1.5rem;
    color: var(--forest-green);
    font-weight: 800;
    position: relative;
}

.section-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--sky-blue) 0%, var(--forest-green) 100%);
    margin: 1rem auto;
    border-radius: 2px;
}

.section-subtitle {
    text-align: center;
    font-size: 1.3rem;
    color: var(--medium-gray);
    margin-bottom: 4rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* Enhanced Popular Cities Section */
.popular-cities-section {
    padding: 80px 0;
    background: var(--soft-beige);
    width: 100%;
}

/* Ensure full width for all sections */
.full-width-content .section {
    width: 100%;
    margin: 0;
}

/* Ensure full width for popular cities section */
.full-width-content .popular-cities-section {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
}

/* No data message styling */
.no-data-message {
    text-align: center;
    padding: 3rem;
    background: var(--white);
    border-radius: 12px;
    border: 2px dashed var(--soft-beige-dark);
    color: var(--medium-gray);
    font-style: italic;
}

.state-cards-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin: 3rem 0;
}

.state-card {
    background: var(--white);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid var(--soft-beige-dark);
    position: relative;
    overflow: hidden;
}

.state-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--forest-green) 0%, var(--sky-blue) 100%);
}

.state-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(47, 97, 48, 0.15);
    border-color: var(--forest-green);
}

.state-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--forest-green);
    margin-bottom: 1.5rem;
    text-align: center;
    border-bottom: 2px solid var(--soft-beige-dark);
    padding-bottom: 1rem;
}

.cities-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.city-link {
    color: var(--medium-gray);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    border-left: 3px solid transparent;
}

.city-link:hover {
    background: var(--soft-beige);
    color: var(--forest-green);
    border-left-color: var(--forest-green);
    text-decoration: none;
    transform: translateX(5px);
}

/* Legacy city cards for backward compatibility */
.cities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.city-card {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid var(--soft-beige);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.city-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--sky-blue) 0%, var(--forest-green) 100%);
}

.city-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(47, 97, 48, 0.15);
    border-color: var(--forest-green);
    text-decoration: none;
}

.city-card h4 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 700;
}

.city-card .zoo-count {
    color: var(--medium-gray);
    font-size: 1rem;
    font-weight: 500;
    background: var(--soft-beige);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: inline-block;
}

/* Enhanced Animal Types Grid - Playful & Engaging */
.animals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.animal-card {
    background: var(--white);
    border-radius: 15px;
    padding: 2rem 1.5rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid var(--soft-beige);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.animal-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--sky-blue) 0%, var(--warm-brown) 100%);
}

.animal-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 25px rgba(125, 200, 247, 0.2);
    border-color: var(--sky-blue);
    text-decoration: none;
}

.animal-card .animal-icon {
    font-size: 3.5rem;
    margin-bottom: 1.25rem;
    transition: transform 0.3s ease;
}

.animal-card:hover .animal-icon {
    transform: scale(1.1) rotate(5deg);
}

.animal-card h4 {
    color: var(--forest-green);
    margin-bottom: 0.75rem;
    font-size: 1.2rem;
    font-weight: 700;
}

.animal-card .zoo-count {
    color: var(--medium-gray);
    font-size: 0.95rem;
    font-weight: 500;
    background: var(--soft-beige);
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    display: inline-block;
}

/* ========================================
   ENHANCED FAQ SECTION - EXPANDABLE & ACCESSIBLE
   ======================================== */

.faq-section {
    background: var(--soft-beige);
    position: relative;
}

.faq-item {
    background: var(--white);
    margin-bottom: 1.5rem;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige-dark);
    transition: all 0.3s ease;
}

.faq-item:hover {
    border-color: var(--forest-green);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.1);
}

.faq-question {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-light) 100%);
    color: var(--white);
    padding: 1.5rem 2rem;
    cursor: pointer;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    min-height: 48px; /* Mobile-friendly tap target */
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.faq-question::after {
    content: '+';
    font-size: 1.5rem;
    font-weight: 700;
    transition: transform 0.3s ease;
}

.faq-question.active::after {
    transform: rotate(45deg);
}

.faq-question:hover {
    background: linear-gradient(135deg, var(--forest-green-light) 0%, var(--forest-green) 100%);
}

.faq-answer {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-answer.active {
    padding: 2rem;
    max-height: 500px;
}

.faq-answer p {
    margin: 0;
    color: var(--dark-charcoal);
    line-height: 1.7;
    font-size: 1.05rem;
}

/* ========================================
   ENHANCED BUTTON SYSTEM - TRUST & ACCESSIBILITY
   ======================================== */

.btn {
    display: inline-block;
    padding: 16px 32px;
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-light) 100%);
    color: var(--white);
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 600;
    font-family: 'Nunito', sans-serif;
    min-height: 48px; /* Mobile-friendly tap target */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.2);
}

.btn:hover {
    background: linear-gradient(135deg, var(--forest-green-light) 0%, var(--forest-green) 100%);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--warm-brown) 0%, var(--warm-brown-light) 100%);
    box-shadow: 0 4px 15px rgba(139, 94, 60, 0.2);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--warm-brown-light) 0%, var(--warm-brown) 100%);
    box-shadow: 0 6px 20px rgba(139, 94, 60, 0.3);
}

.btn-sky {
    background: linear-gradient(135deg, var(--sky-blue) 0%, var(--sky-blue-dark) 100%);
    box-shadow: 0 4px 15px rgba(125, 200, 247, 0.2);
}

.btn-sky:hover {
    background: linear-gradient(135deg, var(--sky-blue-dark) 0%, var(--sky-blue) 100%);
    box-shadow: 0 6px 20px rgba(125, 200, 247, 0.3);
}

/* ========================================
   CITY PAGE ENHANCEMENTS
   ======================================== */

/* City Hero Section - Zoo Style (400px height) */
.city-hero-section {
    position: relative;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
    height: 500px;
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-light) 100%);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4rem;
    overflow: hidden;
}

.city-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(47, 97, 48, 0.8) 0%, rgba(30, 63, 32, 0.9) 100%);
    z-index: 1;
}

.city-hero-section .hero-content-wrapper {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.city-hero-section .hero-content {
    text-align: center;
    color: var(--white);
    max-width: 800px;
}

.city-hero-section .city-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    color: var(--white);
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.city-hero-section .hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 0;
    opacity: 0.95;
    color: var(--white);
}

.city-hero-section .breadcrumbs {
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.city-hero-section .breadcrumbs a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.city-hero-section .breadcrumbs a:hover {
    color: white;
}

.city-hero-section .breadcrumbs .separator {
    margin: 0 0.5rem;
    color: rgba(255, 255, 255, 0.6);
}

.city-hero-section .breadcrumbs .current {
    color: white;
    font-weight: 600;
}

/* ========================================
   NEW 3-COLUMN PETTING ZOO CARDS GRID
   ======================================== */

/* Petting Zoo Cards Grid - 3 Columns */
.petting-zoo-cards-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin: 3rem 0;
}

/* Individual Petting Zoo Card */
.petting-zoo-card {
    background: var(--white);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid var(--soft-beige);
    position: relative;
}

.petting-zoo-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 35px rgba(47, 97, 48, 0.15);
    border-color: var(--forest-green);
}

/* Card Image Section */
.petting-zoo-card .card-image {
    position: relative;
    overflow: hidden;
    height: 220px;
}

.petting-zoo-card .card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.petting-zoo-card:hover .card-image img {
    transform: scale(1.05);
}

/* Rating Badge - Beautiful Style */
.petting-zoo-card .rating-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: rgba(0, 0, 0, 0.85);
    color: var(--white);
    padding: 0.4rem 0.8rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.petting-zoo-card .rating-number {
    font-size: 1rem;
    font-weight: 700;
}

.petting-zoo-card .rating-star {
    color: #ffd700;
    font-size: 1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Card Content */
.petting-zoo-card .card-content {
    padding: 1.5rem;
}

.petting-zoo-card .zoo-name {
    margin-bottom: 0.75rem;
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1.3;
}

.petting-zoo-card .zoo-name a {
    color: var(--forest-green);
    text-decoration: none;
    transition: color 0.3s ease;
}

.petting-zoo-card .zoo-name a:hover {
    color: var(--warm-brown);
}

.petting-zoo-card .zoo-location {
    color: var(--medium-gray);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Activities Section */
.petting-zoo-card .activities-section {
    margin-bottom: 1rem;
}

.petting-zoo-card .activities-section h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--forest-green);
    margin-bottom: 0.5rem;
}

.petting-zoo-card .activity-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.petting-zoo-card .activity-tag {
    background: var(--sky-blue);
    color: var(--white);
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.petting-zoo-card .activity-tag:hover {
    background: var(--sky-blue-dark);
    transform: translateY(-1px);
}

.petting-zoo-card .more-tag {
    background: var(--warm-brown) !important;
}

/* Card Description */
.petting-zoo-card .card-description {
    color: var(--medium-gray);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1.25rem;
}

/* View Details Button */
.petting-zoo-card .view-details-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--forest-green);
    color: var(--white);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    width: 100%;
    justify-content: center;
    margin-top: 0.5rem;
}

.petting-zoo-card .view-details-btn:hover {
    background: var(--warm-brown);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.3);
}

.petting-zoo-card .view-details-btn .arrow {
    transition: transform 0.3s ease;
}

.petting-zoo-card .view-details-btn:hover .arrow {
    transform: translateX(3px);
}

/* Feature zoos styling */
.feature-zoos {
    font-size: 0.9rem;
    color: var(--medium-gray);
    line-height: 1.4;
}

.feature-zoos a {
    color: var(--forest-green);
    text-decoration: none;
    font-weight: 600;
}

.feature-zoos a:hover {
    text-decoration: underline;
}

/* More cities section */
.more-cities-section {
    background: var(--soft-beige);
    padding: 3rem 0;
    margin: 3rem 0;
    border-radius: 20px;
}

.more-cities-section h2 {
    text-align: center;
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 2.2rem;
    font-weight: 700;
}

.more-cities-section > p {
    text-align: center;
    max-width: 600px;
    margin: 0 auto 2rem auto;
    color: var(--medium-gray);
    font-size: 1.1rem;
}

.cities-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    max-width: 800px;
    margin: 0 auto;
}

.city-badge {
    background: white;
    border: 2px solid var(--soft-beige-dark);
    border-radius: 25px;
    padding: 0.8rem 1.5rem;
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 120px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.city-badge:hover {
    border-color: var(--forest-green);
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(47, 97, 48, 0.15);
}

.city-badge .city-name {
    font-weight: 700;
    color: var(--forest-green);
    font-size: 1rem;
    margin-bottom: 0.2rem;
}

.city-badge .zoo-count {
    font-size: 0.8rem;
    color: var(--medium-gray);
}

/* ========================================
   MOBILE-FIRST RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 768px) {
    .hero-section {
        height: 70vh;
        min-height: 500px;
        max-height: 600px;
        padding: 0;
    }

    .hero-content {
        padding: 0 15px;
    }

    .hero-section h1 {
        font-size: 2.2rem;
        margin-bottom: 1rem;
    }

    .hero-section p {
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    /* Ensure mobile full width */
    .full-width-content .hero-section {
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        margin-right: calc(-50vw + 50%);
    }

    .full-width-content .popular-cities-section {
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        margin-right: calc(-50vw + 50%);
    }

    .zoo-finder {
        padding: 2rem;
        margin-top: 2rem;
    }

    .zoo-grid,
    .city-grid,
    .animal-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .cities-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .animals-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    /* State cards responsive */
    .state-cards-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .state-card {
        padding: 1.5rem;
    }

    .state-title {
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .section {
        padding: 3rem 0;
    }
}

/* Tablet responsive styles */
@media (max-width: 1024px) and (min-width: 769px) {
    .state-cards-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

/* ========================================
   UTILITY CLASSES - DESIGN SYSTEM
   ======================================== */

.container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1.5rem;
    width: 100%;
}

/* Full width container for hero section */
.hero-content .container {
    max-width: 1200px;
}

.text-center {
    text-align: center;
}

.mb-2 {
    margin-bottom: 2rem;
}

.mt-2 {
    margin-top: 2rem;
}

.mb-3 {
    margin-bottom: 3rem;
}

.mt-3 {
    margin-top: 3rem;
}

/* ========================================
   REDESIGNED SINGLE ZOO PAGE - FULL WIDTH LAYOUT
   ======================================== */

/* Full-width content for zoo pages */
.zoo-single-page {
    width: 100%;
    max-width: none;
}

/* Hero Section Styling */
.zoo-hero-section {
    position: relative;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
    height: 500px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4rem;
}

.zoo-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(47, 97, 48, 0.6) 0%, rgba(30, 63, 32, 0.7) 100%);
    z-index: 1;
}

.zoo-hero-section .hero-content-wrapper {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.zoo-hero-section .hero-content {
    text-align: center;
    color: var(--white);
    max-width: 800px;
}

.zoo-hero-section .zoo-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    color: var(--white);
}

.zoo-hero-section .zoo-address {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    color: var(--white);
    justify-content: center;
}

.zoo-hero-section .hero-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
}

.hero-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    text-decoration: none;
    border-radius: 50px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.hero-btn:hover {
    background: var(--white);
    color: var(--forest-green);
    border-color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Info + Map Section */
.zoo-info-map-section {
    margin-bottom: 4rem;
    padding: 3rem 0;
    background: var(--white);
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige);
}

.info-map-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.basic-info-panel h2,
.map-panel h3 {
    color: var(--forest-green);
    margin-bottom: 2rem;
    font-size: 2rem;
    font-weight: 700;
}

.zoo-description {
    margin-bottom: 2rem;
}

.zoo-description h3 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.zoo-rating {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--soft-beige);
    border-radius: 15px;
}

.zoo-rating h4 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.rating-stars {
    font-size: 1.2rem;
}

.rating-text {
    font-weight: 600;
    color: var(--dark-charcoal);
}

.opening-hours {
    margin-bottom: 2rem;
}

.opening-hours h4 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.hours-list {
    background: var(--soft-beige);
    border-radius: 10px;
    padding: 1rem;
}

.hours-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(47, 97, 48, 0.1);
}

.hours-row:last-child {
    border-bottom: none;
}

.hours-row .day {
    font-weight: 600;
    color: var(--forest-green);
    flex: 1;
}

.hours-row .time {
    color: var(--dark-charcoal);
    text-align: right;
}

.hours-row .full-line {
    color: var(--dark-charcoal);
    text-align: center;
    width: 100%;
}

.hours-list {
    background: var(--soft-beige);
    padding: 1.5rem;
    border-radius: 10px;
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
}

.contact-info h4 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background: rgba(47, 97, 48, 0.05);
    border-radius: 8px;
}

.contact-item .icon {
    font-size: 1.2rem;
}

.contact-item a {
    color: var(--forest-green);
    text-decoration: none;
    font-weight: 500;
}

.contact-item a:hover {
    text-decoration: underline;
}

/* Map Panel */
.zoo-map-container {
    background: var(--soft-beige);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

#zoo-google-map {
    width: 100%;
    height: 350px;
    border: none;
}

.map-actions {
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-primary {
    background: var(--forest-green);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--warm-brown);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.3);
}

.btn-secondary {
    background: var(--soft-beige);
    color: var(--forest-green);
    border: 2px solid var(--forest-green);
}

.btn-secondary:hover {
    background: var(--forest-green);
    color: var(--white);
}

.btn-outline {
    background: transparent;
    color: var(--forest-green);
    border: 2px solid var(--forest-green);
}

.btn-outline:hover {
    background: var(--forest-green);
    color: var(--white);
}

.quick-info-item {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-light) 100%);
    color: var(--white);
    text-decoration: none;
    border-radius: 10px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    min-height: 48px; /* Mobile-friendly tap target */
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.2);
}

.quick-info-item:hover {
    background: linear-gradient(135deg, var(--forest-green-light) 0%, var(--forest-green) 100%);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.3);
}

.zoo-content-wrapper {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
}

.zoo-main-content section {
    margin-bottom: 3rem;
    padding: 2.5rem;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige);
    transition: all 0.3s ease;
}

.zoo-main-content section:hover {
    border-color: var(--forest-green);
    box-shadow: 0 6px 25px rgba(47, 97, 48, 0.1);
}

.zoo-main-content section h3 {
    color: var(--forest-green);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 3px solid var(--soft-beige);
}

.zoo-rating {
    background: linear-gradient(135deg, var(--soft-beige) 0%, var(--soft-beige-dark) 100%);
    padding: 1.5rem;
    border-radius: 12px;
    margin-top: 1.5rem;
    border: 2px solid var(--forest-green);
}

.rating-stars {
    font-size: 1.4rem;
    margin: 0 0.75rem;
    color: var(--warning-yellow);
}

.exotic-animals-notice {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid var(--warning-yellow);
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    color: #856404;
    font-weight: 600;
}

.animal-tags, .features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 1.5rem;
}

.animal-tag, .feature-tag {
    background: linear-gradient(135deg, var(--soft-beige) 0%, var(--soft-beige-dark) 100%);
    color: var(--forest-green);
    padding: 0.75rem 1.25rem;
    border-radius: 25px;
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid var(--forest-green);
}

.animal-tag:hover, .feature-tag:hover {
    background: var(--forest-green);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.2);
}

.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.activity-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    background: var(--light-gray);
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid var(--soft-beige-dark);
}

.activity-item.available {
    background: linear-gradient(135deg, var(--soft-beige) 0%, var(--soft-beige-dark) 100%);
    border-color: var(--forest-green);
}

.activity-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.activity-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.activity-item:hover .activity-icon {
    transform: scale(1.1);
}

.activity-label {
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--forest-green);
    font-size: 1.1rem;
}

.availability {
    font-size: 0.9rem;
    color: var(--forest-green);
    font-weight: 600;
}

.pros-cons-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2.5rem;
    margin-top: 2rem;
}

.pros-section, .cons-section {
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid;
    transition: all 0.3s ease;
}

.pros-section {
    background: linear-gradient(135deg, var(--soft-beige) 0%, var(--soft-beige-dark) 100%);
    border-color: var(--forest-green);
}

.cons-section {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: var(--warning-yellow);
}

.pros-section:hover, .cons-section:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.pros-section h3 {
    color: var(--forest-green);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    font-weight: 700;
}

.cons-section h3 {
    color: #856404;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    font-weight: 700;
}

.pros-section ul, .cons-section ul {
    list-style: none;
    padding: 0;
}

.pros-section li, .cons-section li {
    padding: 0.5rem 0;
    position: relative;
    padding-left: 2rem;
    font-size: 1.05rem;
    line-height: 1.6;
}

.pros-section li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--forest-green);
    font-weight: bold;
    font-size: 1.2rem;
}

.cons-section li:before {
    content: "⚠";
    position: absolute;
    left: 0;
    color: #856404;
    font-size: 1.2rem;
}

/* Enhanced Sidebar - Information & Trust Signals */
.zoo-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
}

.sidebar-widget {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige);
    transition: all 0.3s ease;
}

.sidebar-widget:hover {
    border-color: var(--forest-green);
    box-shadow: 0 6px 25px rgba(47, 97, 48, 0.1);
}

.sidebar-widget h3 {
    margin-bottom: 1.5rem;
    color: var(--forest-green);
    border-bottom: 3px solid var(--sky-blue);
    padding-bottom: 0.75rem;
    font-size: 1.3rem;
    font-weight: 700;
}

.info-section {
    margin-bottom: 2rem;
}

.info-section h4 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.location-link, .zoo-type-link {
    display: block;
    padding: 1rem;
    background: var(--soft-beige);
    border-radius: 10px;
    text-decoration: none;
    color: var(--dark-charcoal);
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
    border: 2px solid var(--soft-beige-dark);
    font-weight: 500;
}

.location-link:hover, .zoo-type-link:hover {
    background: var(--forest-green);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.2);
}

.map-placeholder {
    text-align: center;
    padding: 3rem 2rem;
    background: linear-gradient(135deg, var(--soft-beige) 0%, var(--soft-beige-dark) 100%);
    border-radius: 15px;
    border: 2px solid var(--forest-green);
    color: var(--medium-gray);
    font-size: 1.1rem;
}

/* ========================================
   NEW SINGLE ZOO PAGE SECTIONS STYLING
   ======================================== */

/* Section Styling */
.zoo-single-page section {
    margin-bottom: 4rem;
    padding: 3rem;
    background: var(--white);
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige);
    transition: all 0.3s ease;
}

.zoo-single-page section:hover {
    border-color: var(--forest-green);
    box-shadow: 0 6px 25px rgba(47, 97, 48, 0.1);
}

.zoo-single-page section h2 {
    color: var(--forest-green);
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-description {
    font-size: 1.1rem;
    color: var(--medium-gray);
    margin-bottom: 2.5rem;
    line-height: 1.6;
}

/* Video Section */
.zoo-video-section .video-container {
    max-width: 800px;
    margin: 0 auto;
}

.responsive-video {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.responsive-video iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 15px;
}

.video-link {
    text-align: center;
    padding: 3rem;
}

.video-link .btn {
    font-size: 1.1rem;
    padding: 1rem 2rem;
}

/* Animals Section */
.zoo-animals-section .animal-tags-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.animal-tag {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem 1rem;
    background: linear-gradient(135deg, var(--soft-beige) 0%, rgba(253, 247, 240, 0.8) 100%);
    border: 2px solid var(--soft-beige);
    border-radius: 15px;
    text-decoration: none;
    color: var(--forest-green);
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    text-align: center;
    min-height: 120px;
    justify-content: center;
}

.animal-tag:hover {
    background: var(--forest-green);
    color: var(--white);
    border-color: var(--forest-green);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.2);
}

.animal-icon-large {
    font-size: 3rem;
    line-height: 1;
}

.animal-name {
    font-size: 20px;
    line-height: 1.2;
}

.exotic-animals-notice {
    background: linear-gradient(135deg, var(--sky-blue) 0%, rgba(135, 206, 235, 0.8) 100%);
    color: var(--white);
    padding: 1.5rem;
    border-radius: 15px;
    margin: 2rem 0;
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: 600;
    font-size: 1.1rem;
    box-shadow: 0 4px 15px rgba(135, 206, 235, 0.3);
}

.exotic-animals-notice .icon {
    font-size: 1.8rem;
}

/* Detailed Information Section */
.zoo-detailed-info-section {
    background: var(--white);
    padding: 4rem 0;
    margin: 4rem 0;
}

.zoo-detailed-info-section h2 {
    text-align: center;
    color: var(--forest-green);
    margin-bottom: 3rem;
    font-size: 2.5rem;
    font-weight: 700;
}

.info-categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.info-category {
    background: var(--soft-beige);
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid rgba(47, 97, 48, 0.1);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.info-category:hover {
    border-color: var(--forest-green);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.1);
}

.info-category h3 {
    color: var(--forest-green);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--white);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.info-item:hover {
    background: rgba(47, 97, 48, 0.05);
}

.info-icon {
    font-size: 1.2rem;
    min-width: 24px;
}

.info-label {
    font-weight: 500;
    color: var(--dark-charcoal);
}

/* Features & Amenities Section */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    background: var(--soft-beige);
    border-radius: 12px;
    border: 2px solid rgba(47, 97, 48, 0.1);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: var(--forest-green);
    color: var(--white);
    border-color: var(--forest-green);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.2);
}

.feature-icon {
    font-size: 1.3rem;
}

.feature-name {
    font-weight: 600;
}

.food-amenities {
    background: var(--soft-beige);
    padding: 2rem;
    border-radius: 15px;
    margin-top: 2rem;
}

.food-amenities h3 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

/* Events Section */
.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.event-card {
    background: var(--soft-beige);
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid rgba(47, 97, 48, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.event-card:hover {
    border-color: var(--forest-green);
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(47, 97, 48, 0.15);
}

.event-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.event-content h4 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.2rem;
    font-weight: 700;
}

.event-content p {
    color: var(--medium-gray);
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.availability-badge {
    background: var(--forest-green);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

/* ========================================
   ENHANCED MOBILE RESPONSIVENESS
   ======================================== */

@media (max-width: 768px) {
    .zoo-hero-section {
        height: 400px;
    }

    .zoo-hero-section .hero-content-wrapper {
        padding: 0 1rem;
    }

    .zoo-hero-section .zoo-title {
        font-size: 2.5rem;
    }

    .zoo-hero-section .zoo-address {
        font-size: 1.1rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .info-map-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .zoo-single-page section {
        padding: 2rem;
        margin-bottom: 2.5rem;
    }

    .zoo-single-page section h2 {
        font-size: 1.8rem;
    }

    .info-categories-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .events-grid {
        grid-template-columns: 1fr;
    }

    .animal-tags-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.75rem;
    }

    .animal-tag {
        padding: 1rem 0.5rem;
        min-height: 100px;
    }

    .animal-icon-large {
        font-size: 2.5rem;
    }

    .animal-name {
        font-size: 0.8rem;
    }
}

/* ========================================
   TESTIMONIALS & REVIEWS SECTION
   ======================================== */

.testimonials-grid {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 2rem;
}

.testimonial-card {
    background: var(--soft-beige);
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid rgba(47, 97, 48, 0.1);
    transition: all 0.3s ease;
    position: relative;
    width: 100%;
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
}

.testimonial-card:hover {
    border-color: var(--forest-green);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.1);
}

.testimonial-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    min-width: 150px;
    flex-shrink: 0;
}

.testimonial-content {
    flex: 1;
}

.reviewer-info h4 {
    color: var(--forest-green);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
    font-weight: 700;
}

.review-date {
    color: var(--medium-gray);
    font-size: 0.9rem;
}

.review-rating {
    display: flex;
    gap: 0.2rem;
}

.star.filled {
    color: #ffd700;
}

.star.empty {
    color: #ddd;
}

.testimonial-content p {
    font-style: italic;
    line-height: 1.6;
    color: var(--dark-charcoal);
}

.reviews-summary {
    background: var(--soft-beige);
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 3rem;
}

.reviews-summary h3 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

/* ========================================
   GOOGLE REVIEWS HIGHLIGHT SECTION
   ======================================== */

.google-reviews-highlight {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 3px solid var(--forest-green);
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
    text-align: center;
    box-shadow: 0 8px 25px rgba(47, 97, 48, 0.15);
}

.google-reviews-header h3 {
    color: var(--forest-green);
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.rating-showcase {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.rating-number {
    font-size: 4rem;
    font-weight: 800;
    color: var(--forest-green);
    line-height: 1;
}

.rating-stars-large {
    display: flex;
    gap: 0.25rem;
    margin: 0.5rem 0;
}

.rating-stars-large .star {
    font-size: 2rem;
}

.rating-count {
    font-size: 1.2rem;
    color: var(--dark-charcoal);
    font-weight: 600;
}

.reviews-summary {
    margin: 2rem 0;
}

.reviews-summary .summary-text {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--dark-charcoal);
    font-style: italic;
    background: var(--soft-beige);
    padding: 1.5rem;
    border-radius: 15px;
    border-left: 4px solid var(--forest-green);
}

/* ========================================
   EXPECTATIONS (PROS/CONS) SECTION
   ======================================== */

.expectations-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.pros-panel, .cons-panel {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.pros-panel {
    border-color: var(--forest-green);
}

.cons-panel {
    border-color: var(--warm-brown);
}

.panel-header {
    padding: 1.5rem;
    text-align: center;
}

.pros-panel .panel-header {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--sky-blue) 100%);
    color: white;
}

.cons-panel .panel-header {
    background: linear-gradient(135deg, var(--warm-brown) 0%, var(--soft-beige) 100%);
    color: var(--dark-charcoal);
}

.panel-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.panel-content {
    padding: 1.5rem;
}

.expectation-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--soft-beige);
}

.expectation-item:last-child {
    border-bottom: none;
}

.item-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
    margin-top: 0.1rem;
}

.item-text {
    flex: 1;
    line-height: 1.5;
    color: var(--dark-charcoal);
}

.pros-panel,
.cons-panel {
    background: var(--soft-beige);
    border-radius: 15px;
    overflow: hidden;
    border: 2px solid rgba(47, 97, 48, 0.1);
    transition: all 0.3s ease;
}

.pros-panel:hover,
.cons-panel:hover {
    border-color: var(--forest-green);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.1);
}

.panel-header {
    background: var(--forest-green);
    color: var(--white);
    padding: 1.5rem;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.panel-content {
    padding: 2rem;
}

.expectation-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--white);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.expectation-item:hover {
    background: rgba(47, 97, 48, 0.05);
    transform: translateX(5px);
}

.expectation-item:last-child {
    margin-bottom: 0;
}

.item-icon {
    font-size: 2rem;
    min-width: 32px;
}

.item-text {
    font-weight: 500;
    line-height: 1.5;
    color: var(--dark-charcoal);
}

.positive .item-icon {
    color: var(--forest-green);
}

.consideration .item-icon {
    color: var(--warm-brown);
}

/* ========================================
   NEARBY ZOOS SECTION
   ======================================== */

.nearby-zoos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.nearby-zoo-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige);
    transition: all 0.3s ease;
}

.nearby-zoo-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(47, 97, 48, 0.15);
    border-color: var(--forest-green);
}

.nearby-zoo-card .zoo-image {
    height: 200px;
    overflow: hidden;
}

.nearby-zoo-card .zoo-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.nearby-zoo-card:hover .zoo-image img {
    transform: scale(1.05);
}

.nearby-zoo-card .zoo-info {
    padding: 2rem;
}

.nearby-zoo-card h4 {
    margin-bottom: 1rem;
    font-size: 1.2rem;
    font-weight: 700;
}

.nearby-zoo-card h4 a {
    color: var(--forest-green);
    text-decoration: none;
}

.nearby-zoo-card h4 a:hover {
    text-decoration: underline;
}

.zoo-location {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    color: var(--medium-gray);
    font-size: 0.9rem;
}

.zoo-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.zoo-rating .stars {
    font-size: 0.9rem;
}

.rating-value {
    font-weight: 600;
    color: var(--dark-charcoal);
    font-size: 0.9rem;
}

.zoo-excerpt {
    color: var(--medium-gray);
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

/* ========================================
   STATE CITIES SECTION
   ======================================== */

.cities-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.city-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: var(--soft-beige);
    color: var(--forest-green);
    text-decoration: none;
    border-radius: 20px;
    border: 2px solid rgba(47, 97, 48, 0.1);
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.city-badge:hover {
    background: var(--forest-green);
    color: var(--white);
    border-color: var(--forest-green);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.2);
}

/* ========================================
   FAQ SECTION
   ======================================== */

.zoo-faq-section {
    background: var(--soft-beige);
    padding: 4rem 0;
    margin: 4rem 0;
    border-radius: 20px;
}

.zoo-faq-section h2 {
    text-align: center;
    color: var(--forest-green);
    margin-bottom: 3rem;
    font-size: 2.5rem;
    font-weight: 700;
}

.zoo-faq-section .section-description {
    text-align: center;
    max-width: 600px;
    margin: 0 auto 3rem auto;
    color: var(--medium-gray);
    font-size: 1.1rem;
    line-height: 1.6;
}

.faq-accordion {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Override any conflicting styles and ensure proper FAQ styling */
.zoo-faq-section .faq-item {
    background: var(--white);
    border-radius: 20px;
    margin-bottom: 1.5rem;
    border: 3px solid var(--soft-beige);
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.zoo-faq-section .faq-item:hover {
    border-color: var(--forest-green);
    box-shadow: 0 8px 25px rgba(47, 97, 48, 0.15);
    transform: translateY(-2px);
}

.zoo-faq-section .faq-question {
    padding: 2rem 2.5rem;
    background: linear-gradient(135deg, var(--forest-green) 0%, rgba(47, 97, 48, 0.9) 100%);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    user-select: none;
    color: var(--white);
    font-size: 1.2rem;
    font-weight: 700;
    line-height: 1.4;
    position: relative;
}

.zoo-faq-section .faq-question:hover {
    background: linear-gradient(135deg, rgba(47, 97, 48, 0.9) 0%, var(--forest-green) 100%);
}

/* Use CSS pseudo-element for the + symbol like homepage */
.zoo-faq-section .faq-question::after {
    content: '+';
    font-size: 2rem;
    font-weight: bold;
    color: var(--white);
    transition: transform 0.3s ease;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.zoo-faq-section .faq-question:hover::after {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.zoo-faq-section .faq-question.active::after {
    transform: rotate(45deg);
}

.zoo-faq-section .faq-answer {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: var(--white);
}

.zoo-faq-section .faq-answer.active {
    padding: 2.5rem;
    max-height: 500px;
}

.zoo-faq-section .faq-answer p {
    margin: 0;
    line-height: 1.7;
    color: var(--dark-charcoal);
    font-size: 1.05rem;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ========================================
   PHOTO GALLERY SECTION
   ======================================== */

.photo-gallery-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-top: 2rem;
}

.gallery-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 15px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(47, 97, 48, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.magnify-icon {
    font-size: 2rem;
    color: var(--white);
}

/* ========================================
   LIGHTBOX MODAL
   ======================================== */

.lightbox-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    text-align: center;
}

.lightbox-close {
    position: absolute;
    top: -50px;
    right: 0;
    color: var(--white);
    font-size: 3rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.lightbox-close:hover {
    color: #ccc;
}

#lightbox-image {
    max-width: 100%;
    max-height: 80vh;
    border-radius: 10px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
}

#lightbox-caption {
    color: var(--white);
    font-size: 1.1rem;
    margin-top: 1rem;
    font-weight: 500;
}

/* ========================================
   TIPS SECTION
   ======================================== */

.tips-panel {
    background: var(--soft-beige);
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    border: 2px solid rgba(47, 97, 48, 0.1);
    transition: all 0.3s ease;
}

.tips-panel:hover {
    border-color: var(--forest-green);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.1);
}

.tips-panel:last-child {
    margin-bottom: 0;
}

.tips-panel h3 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 700;
}

.tips-panel p {
    line-height: 1.6;
    color: var(--dark-charcoal);
    margin: 0;
}

/* ========================================
   ADDITIONAL MOBILE RESPONSIVENESS
   ======================================== */

@media (max-width: 768px) {
    .expectations-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .google-reviews-highlight {
        padding: 1.5rem;
    }

    .rating-number {
        font-size: 3rem;
    }

    .rating-stars-large .star {
        font-size: 1.5rem;
    }

    .panel-header h3 {
        font-size: 1.1rem;
    }

    .panel-content {
        padding: 1rem;
    }

    .nearby-zoos-grid {
        grid-template-columns: 1fr;
    }

    .testimonials-grid {
        flex-direction: column;
        gap: 1rem;
    }

    .testimonial-card {
        flex-direction: column;
        gap: 1rem;
        padding: 1.5rem;
    }

    .testimonial-header {
        min-width: auto;
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-start;
    }

    .photo-gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .faq-question {
        padding: 1.5rem 1.5rem;
    }

    .faq-question h4 {
        font-size: 1rem;
    }

    .faq-toggle {
        width: 35px;
        height: 35px;
        font-size: 1.5rem;
    }

    .faq-answer {
        padding: 1.5rem;
    }

    .tips-panel {
        padding: 1.5rem;
    }

    .lightbox-modal {
        padding: 1rem;
    }

    .lightbox-close {
        top: -40px;
        font-size: 2.5rem;
    }
}

/* ========================================
   ADDITIONAL ENHANCEMENTS - TRUST & ENGAGEMENT
   ======================================== */

/* Sticky Navigation Enhancement */
.site-header {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    background: var(--white);
    border-bottom: 3px solid var(--forest-green);
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--forest-green);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Focus States for Accessibility */
button:focus,
input:focus,
select:focus,
a:focus {
    outline: 3px solid var(--sky-blue);
    outline-offset: 2px;
}

/* Enhanced Touch Interactions for Mobile */
.touch-active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
}

.hover-active {
    transform: translateY(-3px);
}

/* Enhanced Form States */
.form-group.focused,
.zoo-finder.focused {
    transform: scale(1.02);
    transition: transform 0.2s ease;
}

input.error,
select.error {
    border-color: var(--danger-red) !important;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2) !important;
}

/* Enhanced Loading States */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid var(--white);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 0.8s linear infinite;
}

.form-group.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Enhanced Card Filtering Animation */
.zoo-card.filtering,
.city-card.filtering,
.animal-card.filtering {
    opacity: 0.5;
    transform: scale(0.95);
    transition: all 0.3s ease;
}

/* Improved Accessibility Focus States */
.zoo-card:focus,
.city-card:focus,
.animal-card:focus,
.btn:focus {
    outline: 3px solid var(--sky-blue);
    outline-offset: 3px;
    box-shadow: 0 0 0 6px rgba(125, 200, 247, 0.2);
}

/* Enhanced Mobile Navigation */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
        background: var(--forest-green);
        color: var(--white);
        border: none;
        padding: 1rem;
        border-radius: 8px;
        cursor: pointer;
        min-height: 48px;
    }

    .mobile-menu {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--white);
        z-index: 9999;
        padding: 2rem;
    }

    .mobile-menu.active {
        display: block;
    }
}

/* Print Styles */
@media print {
    .hero-section,
    .zoo-finder,
    .sidebar-widget {
        background: white !important;
        color: black !important;
    }

    .btn,
    .mobile-menu-toggle {
        display: none !important;
    }
}

/* ========================================
   ENHANCED CITY PAGE STYLES - SEO FOCUSED
   ======================================== */

/* City Page Header */
.city-page-header {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-dark) 100%);
    color: var(--white);
    padding: 4rem 0;
    position: relative;
}

.city-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.08)"/></svg>');
    pointer-events: none;
}

.city-intro-content {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 12px;
    margin-top: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.city-intro-content p {
    margin: 0;
    font-size: 1.1rem;
    line-height: 1.7;
    opacity: 0.95;
}

/* Enhanced Zoo Grid for City Pages */
.enhanced-zoo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin: 3rem 0;
}

.enhanced-zoo-card {
    background: var(--white);
    border-radius: 16px;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid var(--soft-beige);
    position: relative;
}

.enhanced-zoo-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(47, 97, 48, 0.2);
    border-color: var(--forest-green);
}

.enhanced-zoo-card .zoo-image-link {
    display: block;
    position: relative;
    overflow: hidden;
}

.enhanced-zoo-card img {
    width: 100%;
    height: 240px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.enhanced-zoo-card:hover img {
    transform: scale(1.08);
}

.enhanced-zoo-card .zoo-card-content {
    padding: 2.5rem;
}

.enhanced-zoo-card h3 {
    margin-bottom: 1rem;
    color: var(--forest-green);
    font-size: 1.4rem;
    font-weight: 700;
}

.enhanced-zoo-card .location {
    color: var(--medium-gray);
    margin-bottom: 1.5rem;
    font-size: 1rem;
    font-weight: 500;
}

.enhanced-zoo-card .animal-types {
    background: var(--soft-beige);
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
    border-left: 4px solid var(--sky-blue);
}

.enhanced-zoo-card .animal-types strong {
    color: var(--forest-green);
    font-weight: 600;
}

.enhanced-zoo-card .more-animals {
    color: var(--sky-blue);
    font-weight: 600;
}

.enhanced-zoo-card .feature-tag {
    background: linear-gradient(135deg, var(--sky-blue) 0%, var(--sky-blue-dark) 100%);
    color: var(--white);
    padding: 0.6rem 1.2rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    display: inline-block;
    transition: all 0.3s ease;
}

.enhanced-zoo-card .feature-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(125, 200, 247, 0.4);
}

.enhanced-zoo-card .more-features {
    background: linear-gradient(135deg, var(--warm-brown) 0%, var(--warm-brown-light) 100%);
}

.enhanced-zoo-card .btn-primary {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-light) 100%);
    color: var(--white);
    padding: 1rem 2rem;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    width: 100%;
    text-align: center;
    margin-top: 1rem;
}

.enhanced-zoo-card .btn-primary:hover {
    background: linear-gradient(135deg, var(--forest-green-light) 0%, var(--forest-green) 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.3);
    text-decoration: none;
    color: var(--white);
}

/* City Content Sections */
.city-zoos-section,
.city-family-benefits,
.city-features-section,
.city-tips-section,
.dad-focused-section,
.city-faq-section,
.finder-cta-section {
    margin: 4rem 0;
    padding: 3rem 0;
    position: relative;
}

.city-family-benefits,
.dad-focused-section {
    background: var(--soft-beige);
    border-radius: 20px;
    padding: 4rem 3rem;
    margin: 4rem 0;
}

.city-family-benefits h2,
.city-features-section h2,
.city-tips-section h2,
.dad-focused-section h2,
.city-faq-section h2,
.finder-cta-section h2 {
    color: var(--forest-green);
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-align: left;
}

.city-family-benefits p,
.city-features-section p,
.city-tips-section p,
.dad-focused-section p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--dark-charcoal);
    margin-bottom: 2.5rem;
}

/* Family Benefits Grid */
.family-benefits-grid,
.dad-benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2.5rem;
}

.benefit-item,
.dad-benefit {
    background: var(--white);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border-left: 4px solid var(--sky-blue);
    transition: all 0.3s ease;
}

.benefit-item:hover,
.dad-benefit:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(125, 200, 247, 0.15);
    border-left-color: var(--forest-green);
}

.benefit-item h4,
.dad-benefit h4 {
    color: var(--forest-green);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.benefit-item p,
.dad-benefit p {
    margin: 0;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--medium-gray);
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.feature-item {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige);
    transition: all 0.3s ease;
    text-align: center;
}

.feature-item:hover {
    transform: translateY(-3px);
    border-color: var(--sky-blue);
    box-shadow: 0 6px 20px rgba(125, 200, 247, 0.15);
}

.feature-item h4 {
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.feature-item h4 a {
    color: var(--forest-green);
    text-decoration: none;
    font-weight: 600;
}

.feature-item h4 a:hover {
    color: var(--sky-blue);
}

.feature-item p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--medium-gray);
}

.features-cta {
    background: var(--sky-blue);
    color: var(--white);
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    margin-top: 2rem;
}

.features-cta p {
    margin: 0;
    font-size: 1.05rem;
    font-weight: 500;
}

/* Tips Section */
.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2.5rem;
}

.tip-category {
    background: var(--white);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border-top: 4px solid var(--warm-brown);
    transition: all 0.3s ease;
}

.tip-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(139, 94, 60, 0.15);
    border-top-color: var(--forest-green);
}

.tip-category h4 {
    color: var(--forest-green);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.tip-category ul {
    margin: 0;
    padding-left: 1.5rem;
    list-style-type: none;
}

.tip-category li {
    margin-bottom: 0.75rem;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--medium-gray);
    position: relative;
}

.tip-category li::before {
    content: '✓';
    color: var(--success-green);
    font-weight: bold;
    position: absolute;
    left: -1.5rem;
}

/* Dad Testimonial */
.dad-testimonial {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 15px;
    margin-top: 3rem;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    border-left: 5px solid var(--sky-blue);
    position: relative;
}

.dad-testimonial::before {
    content: '"';
    font-size: 4rem;
    color: var(--sky-blue);
    position: absolute;
    top: 1rem;
    left: 2rem;
    font-family: Georgia, serif;
    opacity: 0.3;
}

.dad-testimonial blockquote {
    margin: 0;
    padding-left: 3rem;
}

.dad-testimonial p {
    font-size: 1.2rem;
    line-height: 1.7;
    color: var(--dark-charcoal);
    font-style: italic;
    margin-bottom: 1rem;
}

.dad-testimonial cite {
    color: var(--forest-green);
    font-weight: 600;
    font-style: normal;
    font-size: 1rem;
}

/* FAQ Section */
.city-faq-section {
    background: var(--soft-beige);
    padding: 4rem 3rem;
    border-radius: 20px;
    margin: 4rem 0;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2.5rem;
}

.faq-item {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige-dark);
    transition: all 0.3s ease;
}

.faq-item:hover {
    border-color: var(--forest-green);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.1);
    transform: translateY(-3px);
}

.faq-item h4 {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-light) 100%);
    color: var(--white);
    padding: 1.5rem;
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    line-height: 1.4;
}

.faq-item p {
    padding: 1.5rem;
    margin: 0;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--medium-gray);
}

/* CTA Section */
.finder-cta-section {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-dark) 100%);
    color: var(--white);
    padding: 4rem 3rem;
    border-radius: 20px;
    margin: 4rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.finder-cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.08)"/></svg>');
    pointer-events: none;
}

.finder-cta-section h2 {
    color: var(--white);
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;
}

.finder-cta-section p {
    color: var(--white);
    opacity: 0.95;
    font-size: 1.1rem;
    margin-bottom: 3rem;
    position: relative;
    z-index: 2;
}

.finder-cta-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.cta-benefits h4 {
    color: var(--white);
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    text-align: left;
}

.cta-benefits ul {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: left;
}

.cta-benefits li {
    margin-bottom: 1rem;
    font-size: 1.05rem;
    position: relative;
    padding-left: 2rem;
}

.cta-benefits li::before {
    content: '🎯';
    position: absolute;
    left: 0;
    top: 0;
}

.cta-action {
    text-align: center;
}

.btn-large {
    padding: 1.5rem 3rem;
    font-size: 1.2rem;
    font-weight: 700;
    border-radius: 15px;
    background: linear-gradient(135deg, var(--sky-blue) 0%, var(--sky-blue-dark) 100%);
    color: var(--white);
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(125, 200, 247, 0.3);
}

.btn-large:hover {
    background: linear-gradient(135deg, var(--sky-blue-dark) 0%, var(--sky-blue) 100%);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(125, 200, 247, 0.4);
    text-decoration: none;
    color: var(--white);
}

.cta-subtext {
    margin-top: 1rem;
    font-size: 0.95rem;
    opacity: 0.8;
    color: var(--white);
}

/* ========================================
   ENHANCED RESPONSIVE DESIGN FOR CITY PAGES
   ======================================== */

/* Tablet Devices (768px to 1024px) */
@media (max-width: 1024px) {
    .enhanced-zoo-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    /* New petting zoo cards responsive */
    .petting-zoo-cards-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .city-hero-section .city-title {
        font-size: 2.5rem;
    }

    .finder-cta-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .cta-benefits {
        text-align: center;
    }

    .cta-benefits h4,
    .cta-benefits ul {
        text-align: center;
    }
}

/* Mobile Devices (up to 768px) */
@media (max-width: 768px) {
    .city-hero-section {
        height: 350px;
    }

    .city-hero-section .hero-content-wrapper {
        padding: 0 1rem;
    }

    .city-hero-section .city-title {
        font-size: 2rem;
    }

    .city-intro-content {
        padding: 1.5rem;
        margin-top: 1.5rem;
    }

    .city-intro-content p {
        font-size: 1rem;
    }

    .enhanced-zoo-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    /* New petting zoo cards mobile */
    .petting-zoo-cards-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin: 2rem 0;
    }

    .petting-zoo-card .card-image {
        height: 200px;
    }

    .petting-zoo-card .card-content {
        padding: 1.25rem;
    }

    .enhanced-zoo-card .zoo-card-content {
        padding: 2rem;
    }

    .enhanced-zoo-card img {
        height: 200px;
    }

    .city-family-benefits,
    .dad-focused-section,
    .city-faq-section,
    .finder-cta-section {
        padding: 2.5rem 1.5rem;
        margin: 2rem 0;
    }

    .city-family-benefits h2,
    .city-features-section h2,
    .city-tips-section h2,
    .dad-focused-section h2,
    .city-faq-section h2,
    .finder-cta-section h2 {
        font-size: 1.8rem;
    }

    .family-benefits-grid,
    .dad-benefits-grid,
    .features-grid,
    .tips-grid,
    .faq-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .benefit-item,
    .dad-benefit,
    .tip-category {
        padding: 1.5rem;
    }

    .dad-testimonial {
        padding: 2rem;
    }

    .dad-testimonial::before {
        font-size: 3rem;
        top: 0.5rem;
        left: 1.5rem;
    }

    .dad-testimonial blockquote {
        padding-left: 2.5rem;
    }

    .dad-testimonial p {
        font-size: 1.1rem;
    }

    .finder-cta-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .btn-large {
        padding: 1.25rem 2.5rem;
        font-size: 1.1rem;
        width: 100%;
        max-width: 300px;
    }

    .cta-benefits li {
        font-size: 1rem;
    }
}

/* Small Mobile Devices (up to 480px) */
@media (max-width: 480px) {
    .city-hero-section {
        height: 300px;
    }

    .city-hero-section .city-title {
        font-size: 1.75rem;
    }

    .city-intro-content {
        padding: 1rem;
        margin-top: 1rem;
    }

    .enhanced-zoo-card .zoo-card-content {
        padding: 1.5rem;
    }

    /* New petting zoo cards small mobile */
    .petting-zoo-card .card-content {
        padding: 1rem;
    }

    .petting-zoo-card .activity-tags {
        gap: 0.25rem;
    }

    .petting-zoo-card .activity-tag {
        padding: 0.3rem 0.6rem;
        font-size: 0.75rem;
    }

    .city-family-benefits,
    .dad-focused-section,
    .city-faq-section,
    .finder-cta-section {
        padding: 2rem 1rem;
        margin: 1.5rem 0;
    }

    .city-family-benefits h2,
    .city-features-section h2,
    .city-tips-section h2,
    .dad-focused-section h2,
    .city-faq-section h2,
    .finder-cta-section h2 {
        font-size: 1.6rem;
    }

    .benefit-item,
    .dad-benefit,
    .tip-category {
        padding: 1.25rem;
    }

    .dad-testimonial {
        padding: 1.5rem;
    }

    .dad-testimonial blockquote {
        padding-left: 2rem;
    }

    .btn-large {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .enhanced-zoo-card .feature-tag {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
        margin-right: 0.25rem;
        margin-bottom: 0.25rem;
    }
}

/* ========================================
   ZOO SINGLE PAGE HERO SECTION
   ======================================== */

.zoo-hero-section {
    position: relative;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
    height: 500px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.zoo-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(47, 97, 48, 0.6) 0%, rgba(30, 63, 32, 0.7) 100%);
    z-index: 1;
}

.zoo-hero-section .hero-content-wrapper {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.zoo-hero-section .hero-content {
    text-align: center;
    color: var(--white);
}

.zoo-hero-section .zoo-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    color: var(--white);
}

.zoo-hero-section .zoo-address {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.95;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.zoo-hero-section .hero-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.zoo-hero-section .hero-btn {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    padding: 0.75rem 1.5rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    backdrop-filter: blur(10px);
}

.zoo-hero-section .hero-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    text-decoration: none;
    color: var(--white);
}

/* ========================================
   NEW PETTING ZOO PAGE SECTIONS STYLES
   ======================================== */

/* Business Status Styles */
.business-status {
    margin-bottom: 1.5rem;
}

.status-display {
    margin-top: 0.5rem;
}

.status-display .status-badge.open {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-display .status-badge.closed {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

/* Zoo Types Styles */
.zoo-types {
    margin-bottom: 1.5rem;
}

.types-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.type-badge {
    background: var(--forest-green);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Feature Cards Styles (matching animal cards) */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem 1rem;
    background: var(--white);
    border: 2px solid var(--soft-beige);
    border-radius: 15px;
    text-decoration: none;
    color: var(--forest-green);
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
    min-height: 120px;
    justify-content: center;
}

.feature-item:hover {
    background: var(--forest-green);
    color: var(--white);
    border-color: var(--forest-green);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.2);
}

.feature-icon-large {
    font-size: 3rem;
    line-height: 1;
}

.feature-name {
    font-size: 20px;
    line-height: 1.2;
}

/* Activities Section Styles */
.zoo-activities-section .activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.activity-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem 1rem;
    background: var(--white);
    border: 2px solid var(--soft-beige);
    border-radius: 15px;
    text-decoration: none;
    color: var(--forest-green);
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
    min-height: 120px;
    justify-content: center;
}

.activity-item:hover {
    background: var(--forest-green);
    color: var(--white);
    border-color: var(--forest-green);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.2);
}

.activity-icon-large {
    font-size: 3rem;
    line-height: 1;
}

.activity-name {
    font-size: 20px;
    line-height: 1.2;
}

.activity-status.available {
    color: #22c55e;
}

.activity-status.unavailable {
    color: #ef4444;
}

/* Tips for Families Section Styles */
.zoo-tips-families-section .tips-content {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 2rem;
    background: var(--soft-beige);
    border-radius: 15px;
    border-left: 5px solid var(--forest-green);
}

.tips-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.tips-text {
    flex: 1;
}

.tips-text p {
    margin: 0;
    font-size: 1.1rem;
    line-height: 1.7;
}

/* Nearby Conveniences Section Styles */
.zoo-nearby-conveniences-section {
    background: var(--soft-beige);
    padding: 3rem 2rem;
    border-radius: 20px;
    margin: 3rem 0;
}

.nearby-conveniences-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.convenience-card {
    background: var(--white);
    border-radius: 15px;
    padding: 2rem;
    border: 2px solid rgba(47, 97, 48, 0.1);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.convenience-card:hover {
    border-color: var(--forest-green);
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(47, 97, 48, 0.15);
}

.convenience-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.convenience-icon {
    font-size: 3rem;
    flex-shrink: 0;
}

.convenience-info {
    flex: 1;
}

.convenience-name {
    color: var(--forest-green);
    font-size: 1.2rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    line-height: 1.3;
}

.convenience-type {
    color: var(--medium-gray);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.convenience-status {
    flex-shrink: 0;
}

.status-badge.open {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.status-badge.closed {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.convenience-details {
    margin-bottom: 1.5rem;
}

.convenience-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.convenience-rating .rating-value {
    font-weight: 700;
    color: var(--forest-green);
    font-size: 1.1rem;
}

.convenience-rating .rating-count {
    color: var(--medium-gray);
    font-size: 0.9rem;
}

.convenience-hours {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--soft-beige);
    border-radius: 10px;
}

.hours-label {
    font-weight: 600;
    color: var(--forest-green);
}

.hours-time {
    color: var(--dark-charcoal);
    font-weight: 500;
}

.convenience-amenities {
    margin-bottom: 1rem;
}

.amenity-icons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.amenity-icon {
    font-size: 1.5rem;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.amenity-icon.available {
    background: rgba(34, 197, 94, 0.1);
    border: 2px solid rgba(34, 197, 94, 0.3);
}

.amenity-icon.unavailable {
    background: rgba(156, 163, 175, 0.1);
    border: 2px solid rgba(156, 163, 175, 0.3);
    opacity: 0.5;
}

.convenience-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* ========================================
   NEARBY ZOOS SPLIT LAYOUT
   ======================================== */

/* Split layout container */
.nearby-zoos-section-split {
    padding: 3rem 0;
    background: var(--light-gray);
}

.nearby-zoos-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Left Panel - Current Zoo Info */
.current-zoo-panel {
    background: var(--white);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige-dark);
}

.current-zoo-panel h2 {
    color: var(--forest-green);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--soft-beige-dark);
}

.info-item:last-child {
    border-bottom: none;
}

.info-icon {
    font-size: 1.2rem;
    min-width: 24px;
}

.status-open {
    color: var(--success-green);
}

.info-text {
    color: var(--dark-charcoal);
    font-weight: 500;
    line-height: 1.4;
}

.info-link {
    color: var(--sky-blue-dark);
    text-decoration: none;
    font-weight: 500;
}

.info-link:hover {
    color: var(--forest-green);
    text-decoration: underline;
}

.status-item .info-text {
    color: var(--success-green);
    font-weight: 600;
}

/* Hours summary */
.hours-summary {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 2px solid var(--soft-beige-dark);
}

.day-hours {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--soft-beige);
}

.day-hours.today {
    background: var(--soft-beige);
    padding: 0.75rem;
    border-radius: 8px;
    border-bottom: none;
    font-weight: 600;
    color: var(--forest-green);
}

.day-hours .day {
    font-weight: 500;
    color: var(--medium-gray);
}

.day-hours.today .day {
    color: var(--forest-green);
}

.day-hours .time {
    font-weight: 500;
    color: var(--dark-charcoal);
}

/* Right Panel - Map and Nearby Zoos */
.map-and-nearby-panel {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.nearby-map-container {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige-dark);
}

#nearby-zoos-map {
    width: 100%;
    height: 300px;
    background: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--medium-gray);
    font-style: italic;
}

/* Nearby zoos list */
.nearby-zoos-list {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige-dark);
}

.nearby-zoos-list h3 {
    color: var(--forest-green);
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
    text-align: center;
}

.nearby-zoo-item {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--soft-beige-dark);
}

.nearby-zoo-item:last-child {
    border-bottom: none;
}

.zoo-thumbnail {
    flex-shrink: 0;
}

.zoo-thumbnail img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid var(--soft-beige-dark);
}

.zoo-details {
    flex: 1;
}

.zoo-details h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
}

.zoo-details h4 a {
    color: var(--forest-green);
    text-decoration: none;
}

.zoo-details h4 a:hover {
    color: var(--forest-green-dark);
    text-decoration: underline;
}

.zoo-address {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--medium-gray);
    font-size: 0.9rem;
}

.zoo-distance {
    margin-bottom: 0.75rem;
}

.distance-badge {
    background: var(--sky-blue);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.view-details-btn {
    background: var(--forest-green);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
}

.view-details-btn:hover {
    background: var(--forest-green-dark);
    transform: translateY(-1px);
    text-decoration: none;
    color: var(--white);
}

.no-nearby-zoos {
    text-align: center;
    color: var(--medium-gray);
    font-style: italic;
    padding: 2rem;
}

/* Mobile responsive for nearby zoos split layout */
@media (max-width: 768px) {
    .nearby-zoos-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .current-zoo-panel,
    .nearby-zoos-list {
        padding: 1.5rem;
    }

    .nearby-zoo-item {
        flex-direction: column;
        text-align: center;
    }

    .zoo-thumbnail {
        align-self: center;
    }

    /* Mobile styles for new sections */
    .features-grid,
    .zoo-activities-section .activities-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 0.75rem;
    }

    .feature-item,
    .activity-item {
        padding: 1rem 0.75rem;
        min-height: 100px;
    }

    .feature-icon-large,
    .activity-icon-large {
        font-size: 2.5rem;
    }

    .feature-name,
    .activity-name {
        font-size: 16px;
    }

    .zoo-nearby-conveniences-section {
        padding: 2rem 1rem;
    }

    .nearby-conveniences-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .convenience-card {
        padding: 1.5rem;
    }

    .convenience-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 1rem;
    }

    .convenience-icon {
        font-size: 2.5rem;
    }

    .convenience-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .convenience-actions .btn {
        width: 100%;
        justify-content: center;
        text-align: center;
    }

    .types-list {
        flex-direction: column;
        align-items: flex-start;
    }

    .type-badge {
        font-size: 0.8rem;
        padding: 0.2rem 0.6rem;
    }

    /* Adjust base font size for mobile */
    body {
        font-size: 16px;
    }
}

/* ========================================
   NEW SPLIT LAYOUT FOR PETTING ZOO PAGES
   ======================================== */

/* New Split Layout for Petting Zoo Pages */
.zoo-split-layout {
    display: flex;
    gap: 2rem;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 2rem;
}

.zoo-main-content {
    flex: 0 0 800px;
    width: 800px;
}

.zoo-sidebar {
    flex: 0 0 480px;
    width: 480px;
}

/* Main Content Styles */
.zoo-info-section {
    background: var(--white);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige);
    margin-bottom: 2rem;
}

.zoo-info-section h2 {
    color: var(--forest-green);
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 2rem;
    border-bottom: 3px solid var(--forest-green);
    padding-bottom: 0.5rem;
}

.zoo-info-section h3, .zoo-info-section h4 {
    color: var(--forest-green);
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.location-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid var(--soft-beige);
}

/* Sidebar Styles */
.zoo-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.nearby-zoos-section,
.other-cities-section {
    background: var(--white);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige);
}

.nearby-zoos-section h2,
.other-cities-section h2 {
    color: var(--forest-green);
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-align: center;
}

/* Nearby Zoos Grid */
.nearby-zoos-grid {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Sidebar Petting Zoo Cards - Compact Version */
.zoo-sidebar .petting-zoo-card {
    margin-bottom: 1.5rem;
}

.zoo-sidebar .petting-zoo-card .card-image {
    height: 140px;
}

.zoo-sidebar .petting-zoo-card .card-content {
    padding: 1.25rem;
}

.zoo-sidebar .petting-zoo-card h3 {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
}

.zoo-sidebar .petting-zoo-card .zoo-excerpt {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.zoo-sidebar .petting-zoo-card .view-details-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.85rem;
}

.nearby-zoo-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--soft-beige);
    transition: all 0.3s ease;
}

.nearby-zoo-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 25px rgba(47, 97, 48, 0.15);
    border-color: var(--forest-green);
}

.nearby-zoo-card .card-image {
    position: relative;
    height: 120px;
    overflow: hidden;
}

.nearby-zoo-card .card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.nearby-zoo-card:hover .card-image img {
    transform: scale(1.05);
}

.nearby-zoo-card .rating-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.2rem;
}

.nearby-zoo-card .card-content {
    padding: 1rem;
}

.nearby-zoo-card h3 {
    margin-bottom: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    color: var(--forest-green);
}

.nearby-zoo-card h3 a {
    color: inherit;
    text-decoration: none;
}

.nearby-zoo-card h3 a:hover {
    color: var(--warm-brown);
}

.nearby-zoo-card .zoo-excerpt {
    font-size: 0.85rem;
    color: var(--medium-gray);
    margin-bottom: 1rem;
    line-height: 1.4;
}

.nearby-zoo-card .card-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.nearby-zoo-card .btn {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    border-radius: 8px;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
}

.nearby-zoo-card .btn-primary {
    background: var(--forest-green);
    color: var(--white);
    border: none;
}

.nearby-zoo-card .btn-primary:hover {
    background: var(--warm-brown);
    transform: translateY(-1px);
}

.nearby-zoo-card .distance-info,
.zoo-sidebar .petting-zoo-card .distance-info {
    background: var(--soft-beige);
    color: var(--forest-green);
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    margin-top: 0.5rem;
}

/* Cities Badges */
.cities-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.8rem;
}

.city-badge {
    background: var(--soft-beige);
    color: var(--forest-green);
    padding: 0.6rem 1rem;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.city-badge:hover {
    background: var(--forest-green);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(47, 97, 48, 0.3);
}

.city-badge .zoo-count {
    background: rgba(255, 255, 255, 0.3);
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 1320px) {
    .zoo-split-layout {
        max-width: 100%;
        padding: 0 1rem;
    }
}

@media (max-width: 1024px) {
    .zoo-split-layout {
        flex-direction: column;
        gap: 2rem;
    }

    .zoo-main-content,
    .zoo-sidebar {
        flex: none;
        width: 100%;
    }

    .zoo-sidebar {
        order: -1; /* Move sidebar above main content on mobile */
    }
}

@media (max-width: 768px) {
    .zoo-info-section,
    .nearby-zoos-section,
    .other-cities-section {
        padding: 1.5rem;
    }

    .nearby-zoo-card .card-content {
        padding: 1rem;
    }

    .cities-badges {
        gap: 0.5rem;
    }

    .city-badge {
        padding: 0.5rem 0.8rem;
        font-size: 0.85rem;
    }
}
